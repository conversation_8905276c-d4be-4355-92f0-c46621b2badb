import pygame
import math

pygame.init()
screen = pygame.display.set_mode((800, 600))
clock = pygame.time.Clock()

def draw_stickman(x, y, arm_angle):
    # Head
    pygame.draw.circle(screen, (255, 255, 255), (x, y), 20)
    # Body
    pygame.draw.line(screen, (255, 255, 255), (x, y+20), (x, y+100), 3)
    # Arms with animation
    arm_x = x + 30 * math.cos(arm_angle)
    arm_y = y + 50 + 10 * math.sin(arm_angle)
    pygame.draw.line(screen, (255, 255, 255), (x, y+50), (arm_x, arm_y), 3)
    # Legs
    pygame.draw.line(screen, (255, 255, 255), (x, y+100), (x-20, y+150), 3)
    pygame.draw.line(screen, (255, 255, 255), (x, y+100), (x+20, y+150), 3)

running = True
angle = 0
while running:
    screen.fill((0, 0, 0))
    draw_stickman(400, 200, angle)
    angle += 0.1
    pygame.display.flip()
    clock.tick(60)